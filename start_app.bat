@echo off
echo Starting E-commerce Chatbot Application...
echo.
echo Starting Backend Server...
start "Backend Server" cmd /k start_backend.bat
echo Waiting for backend to start...
timeout /t 5 /nobreak > nul
echo.
echo Starting Frontend Server...
start "Frontend Server" cmd /k start_frontend.bat
echo.
echo ========================================
echo   E-commerce Chatbot Application
echo ========================================
echo.
echo Backend API: http://localhost:5000
echo Frontend:    http://localhost:5174
echo Health Check: http://localhost:5000/api/health
echo.
echo Demo Credentials:
echo Username: testuser
echo Password: test123
echo.
echo Both servers are starting in separate windows...
echo You can close this window once both servers are running.
echo.
pause
