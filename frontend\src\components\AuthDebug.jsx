import React from 'react';
import { useAuth } from '../hooks/useAuth.jsx';
import { getToken, getUser } from '../utils/auth';

const AuthDebug = () => {
  const { user, loading, isAuthenticated } = useAuth();
  
  return (
    <div className="fixed bottom-4 left-4 bg-black bg-opacity-75 text-white p-4 rounded-lg text-xs max-w-xs">
      <h4 className="font-bold mb-2">Auth Debug</h4>
      <div className="space-y-1">
        <div>Loading: {loading ? 'true' : 'false'}</div>
        <div>Authenticated: {isAuthenticated ? 'true' : 'false'}</div>
        <div>User: {user ? user.username : 'null'}</div>
        <div>Token: {getToken() ? 'exists' : 'null'}</div>
        <div>Stored User: {getUser() ? getUser().username : 'null'}</div>
      </div>
    </div>
  );
};

export default AuthDebug;
