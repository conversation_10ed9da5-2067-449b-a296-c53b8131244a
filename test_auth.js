// Simple test script to verify authentication flow
const testLogin = async () => {
  try {
    console.log('Testing login API...');
    
    const response = await fetch('http://localhost:5000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'testuser',
        password: 'test123'
      })
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Login successful!');
      console.log('User:', data.user);
      console.log('Token received:', !!data.access_token);
      
      // Test token verification
      const verifyResponse = await fetch('http://localhost:5000/api/auth/verify-token', {
        headers: {
          'Authorization': `Bearer ${data.access_token}`
        }
      });
      
      if (verifyResponse.ok) {
        console.log('✅ Token verification successful!');
      } else {
        console.log('❌ Token verification failed');
      }
      
    } else {
      console.log('❌ Login failed:', data.error);
    }
  } catch (error) {
    console.log('❌ Network error:', error.message);
  }
};

testLogin();
