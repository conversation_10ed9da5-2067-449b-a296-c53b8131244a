from .. import Provider as PhoneNumberProvider


class Provider(PhoneNumberProvider):
    formats = (
        "+39 {{area_code}}#####!!",
        "{{area_code}}#####!!",
    )

    area_codes = (
        # Landline: https://en.wikipedia.org/wiki/List_of_dialling_codes_in_Italy
        "010#",
        "011#",
        "0122",
        "0123",
        "0124",
        "0125",
        "0131",
        "0141",
        "015#",
        "0161",
        "0163",
        "0165",
        "0166",
        "0171",
        "0183",
        "0184",
        "0185",
        "0187",
        "019#",
        "02##",
        "030#",
        "031#",
        "0321",
        "0322",
        "0324",
        "0331",
        "0332",
        "0341",
        "0342",
        "0343",
        "0344",
        "0345",
        "035#",
        "0362",
        "0363",
        "0364",
        "0365",
        "0371",
        "0372",
        "0373",
        "0375",
        "0376",
        "0382",
        "039#",
        "040#",
        "041#",
        "0421",
        "0422",
        "0423",
        "0424",
        "0425",
        "0426",
        "0429",
        "0431",
        "0432",
        "0434",
        "0438",
        "0444",
        "0445",
        "045#",
        "0461",
        "0471",
        "0481",
        "049#",
        "050#",
        "051#",
        "0521",
        "0522",
        "0523",
        "0532",
        "0535",
        "0536",
        "0541",
        "0543",
        "0544",
        "0545",
        "0547",
        "0549",
        "055#",
        "0564",
        "0565",
        "0566",
        "0571",
        "0572",
        "0573",
        "0574",
        "0575",
        "0577",
        "0578",
        "0583",
        "0584",
        "0585",
        "0586",
        "0587",
        "0588",
        "059#",
        "06##",
        "070#",
        "071#",
        "0721",
        "0731",
        "0732",
        "0733",
        "0734",
        "0735",
        "0736",
        "0737",
        "075#",
        "0761",
        "0765",
        "0771",
        "0773",
        "0774",
        "0775",
        "0776",
        "0782",
        "0783",
        "0784",
        "0789",
        "079#",
        "080#",
        "081#",
        "0823",
        "0824",
        "0825",
        "0832",
        "085#",
        "0861",
        "0862",
        "0865",
        "0874",
        "0881",
        "0882",
        "0883",
        "0884",
        "089#",
        "0835",
        "090#",
        "091#",
        "0921",
        "0931",
        "0932",
        "0933",
        "0923",
        "0922",
        "0925",
        "0934",
        "0941",
        "0942",
        "095#",
        "0961",
        "0962",
        "0963",
        "0965",
        "0974",
        "0975",
        "099#",
        "0984",
        # Mobile: https://it.wikipedia.org/wiki/Prefissi_telefonici_dei_cellulari_italiani
        "3513",
        "3514",
        "3515",
        "3516",
        "3517",
        "3518",
        "3519",
        "3520",
        "330#",
        "331#",
        "333#",
        "334#",
        "335#",
        "336#",
        "337#",
        "338#",
        "339#",
        "360#",
        "361#",
        "362#",
        "363#",
        "366#",
        "368#",
        "381#",
        "385#",
        "340#",
        "341#",
        "342#",
        "343#",
        "344#",
        "345#",
        "346#",
        "347#",
        "348#",
        "349#",
        "383#",
        "320#",
        "322#",
        "323#",
        "324#",
        "327#",
        "328#",
        "329#",
        "355#",
        "380#",
        "388#",
        "389#",
        "390#",
        "391#",
        "392#",
        "393#",
        "397#",
        "3773",
        "3793",
        "3710",
        "3777",
        "3311",
        "3703",
        "3534",
        "3778",
        "3533",
        "373#",
        "3755",
        "3756",
        "3757",
        "3770",
        "3791",
        "3792",
        "3500",
        "3501",
        "3505",
        "3509",
        "3510",
        "3511",
        "3512",
        "373#",
        "382#",
        "3780",
        "3500",
        "3505",
        "3760",
        "3711",
        "3713",
        "3714",
        "3715",
        "3716",
        "3772",
        "3774",
        "3776",
        "3779",
        "3701",
        "3783",
    )

    def area_code(self) -> str:
        return self.numerify(self.random_element(self.area_codes))

    def phone_number(self) -> str:
        pattern: str = self.random_element(self.formats)
        return self.numerify(self.generator.parse(pattern))
