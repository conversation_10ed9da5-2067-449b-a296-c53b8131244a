from .. import Provider as AddressProvider


class Provider(AddressProvider):
    city_formats = ("{{first_name}}",)
    city_suffixes = ("şəhəri", "ş.")
    street_suffixes = ("küçəsi", "küç.", "prospekti", "pr.")
    village_suffixes = ("kəndi", "k.")
    district_suffixes = ("rayonu", "ray.")
    settlement_suffixes = ("qəsəbəsi", "qəs.")

    building_number_formats = ("#",)
    house_number_formats = ("#", "##", "###")
    address_formats = (
        "{{city}} {{city_suffix }}, {{street_name}} {{street_suffix}}, b. {{building_number}}, m. {{house_number}}",
        "{{district}} {{district_suffix }}, {{street_name}} {{street_suffix}}, b. {{building_number}},"
        " m. {{house_number}}",
        "{{district}} {{district_suffix }}, {{village}} {{village_suffix}}, {{street_name}} {{street_suffix}}",
        "{{district}} {{district_suffix }}, {{settlement}} {{settlement_suffix}}, {{street_name}} {{street_suffix}},"
        " b. {{building_number}}, m. {{house_number}}",
    )
    street_name_formats = ("{{street}}",)
    cities = [
        "Bakı",
        "Gəncə",
        "Naxçıvan",
        "Xankəndi",
        "Lənkəran",
        "Mingəçevir",
        "Naftalan",
        "Sumqayıt",
        "Şəki",
        "Şirvan",
        "Yevlax",
    ]
    countries = [
        "Abxaziya",
        "Akrotiri və Dekeliya",
        "Aland adaları",
        "Albaniya",
        "Almaniya",
        "Amerika Samoası",
        "Andorra Knyazlığı",
        "Angilya",
        "Anqola",
        "Antiqua və Barbuda",
        "Argentina Respublikası",
        "Aruba",
        "Avstraliya",
        "Avstriya",
        "Azərbaycan",
        "Baham adaları",
        "Banqladeş",
        "Barbados",
        "Belçika",
        "Beliz",
        "Belarus",
        "Benin",
        "Bermud adaları",
        "Birləşmiş Ərəb Əmirlikləri",
        "ABŞ",
        "Boliviya",
        "Bolqarıstan",
        "Bosniya və Herseqovina",
        "Botsvana",
        "Böyük Britaniya",
        "Braziliya",
        "Bruney",
        "Burkina",
        "Burundi",
        "Butan",
        "Bəhreyn",
        "Cersi",
        "Cəbəllütariq",
        "Cənubi Afrika Respublikası",
        "Cənubi Sudan",
        "Cənubi Koreya",
        "Cibuti",
        "Çad",
        "Çexiya",
        "Monteneqro",
        "Çili",
        "Çin",
        "Danimarka",
        "Dominika",
        "Dominikan",
        "Efiopiya",
        "Ekvador",
        "Ekvatorial",
        "Eritreya",
        "Ermənistan",
        "Estoniya",
        "Əfqanıstan",
        "Əlcəzair",
        "Farer adaları",
        "Fələstin",
        "Fici",
        "Fil Dişi Sahili",
        "Filippin",
        "Finlandiya",
        "Folklend adaları",
        "Fransa",
        "Fransa Polineziyası",
        "Gernsi",
        "Gürcüstan",
        "Haiti",
        "Hindistan",
        "Honduras",
        "Honkonq",
        "Xorvatiya",
        "İndoneziya",
        "İordaniya",
        "İraq",
        "İran",
        "İrlandiya",
        "İslandiya",
        "İspaniya",
        "İsrail",
        "İsveç",
        "İsveçrə",
        "İtaliya",
        "Kabo-Verde",
        "Kamboca",
        "Kamerun",
        "Kanada",
        "Kayman adaları",
        "Keniya",
        "Kipr",
        "Kiribati",
        "Kokos adaları",
        "Kolumbiya",
        "Komor adaları",
        "Konqo Respublikası",
        "Konqo Demokratik Respublikası",
        "Kosovo",
        "Kosta-Rika",
        "Kuba",
        "Kuk adaları",
        "Küveyt",
        "Qabon",
        "Qambiya",
        "Qana",
        "Qətər",
        "Qayana",
        "Qazaxıstan",
        "Qərbi Saxara",
        "Qırğızıstan",
        "Qrenada",
        "Qrenlandiya",
        "Quam",
        "Qvatemala",
        "Qvineya",
        "Qvineya-Bisau",
        "Laos",
        "Latviya",
        "Lesoto",
        "Liberiya",
        "Litva",
        "Livan",
        "Liviya",
        "Lixtenşteyn",
        "Lüksemburq",
        "Macarıstan",
        "Madaqaskar",
        "Makao",
        "Şimali Makedoniya",
        "Malavi",
        "Malayziya",
        "Maldiv adaları",
        "Mali",
        "Malta",
        "Marşall adaları",
        "Mavriki",
        "Mavritaniya",
        "Mayotta",
        "Meksika",
        "Men adası",
        "Mərakeş",
        "Mərkəzi Afrika Respublikası",
        "Mikroneziya",
        "Milad adası",
        "Misir",
        "Myanma",
        "Moldova",
        "Monako",
        "Monqolustan",
        "Montserrat",
        "Mozambik",
        "Müqəddəs Yelena adası",
        "Namibiya",
        "Nauru",
        "Nepal",
        "Niderland",
        "Niger",
        "Nigeriya",
        "Nikaraqua",
        "Norveç",
        "Oman",
        "Özbəkistan",
        "Pakistan",
        "Palau",
        "Panama",
        "Papua",
        "Paraqvay",
        "Peru",
        "Pitkern adaları",
        "Polşa",
        "Portuqaliya",
        "Puerto-Riko",
        "Ruanda",
        "Rumıniya",
        "Rusiya",
        "Salvador",
        "Samoa",
        "San-Marino",
        "San-Tome və Prinsipi",
        "Seneqal",
        "Sen-Bartelemi",
        "Sent-Kits və Nevis",
        "Sent-Lüsiya",
        "Sen-Marten",
        "Sen-Pyer və Mikelon",
        "Sent-Vinsent və Qrenadin",
        "Serbiya",
        "Seyşel adaları",
        "Səudiyyə Ərəbistanı",
        "Sinqapur",
        "Slovakiya",
        "Sloveniya",
        "Solomon adaları",
        "Somali",
        "Somalilend",
        "Sudan",
        "Surinam",
        "Suriya",
        "Esvatini",
        "Syerra-Leone",
        "Şərqi Timor",
        "Şimali Kipr Türk Respublikası",
        "Şimali Koreya",
        "Şimali Marian adaları",
        "Şri-Lanka",
        "Tacikistan",
        "Tanzaniya",
        "Tailand",
        "Tonqa",
        "Toqo",
        "Trinidad və Tobaqo",
        "Tunis",
        "Tuvalu",
        "Türkiyə",
        "Türkmənistan",
        "Ukrayna",
        "Uqanda",
        "Uruqvay",
        "Vanuatu",
        "Vatikan",
        "Venesuela",
        "Vyetnam",
        "Yamayka",
        "Yaponiya",
        "Yeni Kaledoniya",
        "Yeni Zelandiya",
        "Yəmən",
        "Yunanıstan",
        "Zambiya",
        "Zimbabve",
    ]
    districts = [
        "Abşeron",
        "Ağcabədi",
        "Ağdam",
        "Ağdaş",
        "Ağstafa",
        "Ağsu",
        "Astara",
        "Babək",
        "Balakən",
        "Beyləqan",
        "Bərdə",
        "Biləsuvar",
        "Cəbrayıl",
        "Cəlilabad",
        "Culfa",
        "Daşkəsən",
        "Füzuli",
        "Gədəbəy",
        "Goranboy",
        "Göyçay",
        "Göygöl",
        "Hacıqabul",
        "Xaçmaz",
        "Xızı",
        "Xocalı",
        "Xocavənd",
        "İmişli",
        "İsmayıllı",
        "Kəlbəcər",
        "Kəngərli",
        "Kürdəmir",
        "Qax",
        "Qazax",
        "Qəbələ",
        "Qobustan",
        "Quba",
        "Qubadlı",
        "Qusar",
        "Laçın",
        "Lerik",
        "Lənkəran",
        "Masallı",
        "Neftçala",
        "Oğuz",
        "Ordubad*",
        "Saatlı",
        "Sabirabad",
        "Salyan",
        "Samux",
        "Sədərək",
        "Siyəzən",
        "Şabran",
        "Şahbuz",
        "Şamaxı",
        "Şəki",
        "Şəmkir",
        "Şərur",
        "Şuşa",
        "Tərtər",
        "Tovuz",
        "Ucar",
        "Yardımlı",
        "Yevlax",
        "Zaqatala",
        "Zəngilan",
        "Zərdab",
    ]
    settlements = [
        "Ləki",
        "Nehrəm",
        "Soyuqbulaq",
        "Şəkərli",
        "Cəhri",
        "Qarayazi",
        "Həzi asalanov",
        "Poylu",
        "Ceyrançöl",
        "Saloğlu",
        "Vurğun",
        "Qəhramanli",
        "Yuxari aran",
        "Mayak",
        "Milabad",
        "Örənqala",
        "Baharabad",
        "Günəş",
        "Orconikidze",
        "Digah",
        "Güzdək",
        "Hökməli",
        "Qobu",
        "Mehdiabad",
        "Saray",
        "Taxtakörpü",
        "Hindarx",
        "Şərq",
        "Sarisu",
        "Mil",
        "Türklər",
        "Bahar",
        "Babək",
        "Keşlə",
        "Qabaqçöl",
        "Xindiristan",
        "Bənövşələr",
        "Birinci alibəyli",
        "Birinci baharli",
        "Birinci dördyol",
        "Birinci quzanli",
        "İkinci alibəyli",
        "İkinci baharli",
        "İkinci dördyol",
        "İmamqulubəyli",
        "Qasimbəyli",
        "Səfərli",
        "Təzəkənd",
        "Ergi",
        "Yeni ergi",
        "Aşaği qəsil kəndi",
        "Orta qəsil kəndi",
        "Türyançay",
        "Yuxari qəsil kəndi",
        "Birinci zobucuq",
        "İkinci zobucuq",
        "Üçüncü zobucuq",
        "Dördüncü zobucuq",
        "Beşinci zobucuq",
        "Fin",
        "Horadiz",
        "Qayidiş 2",
        "Hacikənd",
        "Yuxari ağcakənd",
        "Qizilhacili",
        "Goran",
        "Aşaği ağcakənd",
        "Qazanbulaq",
        "Kürəkçay",
        "Ayaq qərvənd",
        "Quzanli",
        "Navahi",
        "Pirsaat",
        "Muğan",
        "Padar",
        "Navahi",
        "Vətəgə",
        "Bəhrəmtəpə",
        "Lahic",
        "Basqal",
        "Şəfəq",
        "Yeni mil",
        "Xocahəsən",
        "Sulutəpə",
        "Biləcəri",
        "Binəqədi",
        "28 may",
        "Rəsulzadə",
        "Qumlaq",
        "Xələfli",
        "Xudafərin",
        "Mahmudlu",
        "Novoqolovka",
        "Alunitdağ",
        "Quşçu körpüsü",
        "Daşkəsən",
        "Quşçu",
        "Yuxari daşkəsən",
        "1 nömrəli qayidiş",
        "2 nömrəli qayidiş",
    ]
    streets = [
        "A.AĞAYEV",
        "A.M.CÜMƏ",
        "TƏBRİZ",
        "XALİQ ABBASOV",
        "İSLAM ABIŞOV",
        "HEYDƏR ƏLİYEV",
        "XƏTAİ",
        "GÖL ƏTRAFI",
        "Z.XƏLİL",
        "2-Cİ SÜLH",
        "Q.PİRİMOV",
        "ASİF MƏMMƏDOV",
        "R.HACIYEV",
        "FƏXRƏDDİN ƏSƏDOV",
        "K.MARKS",
        "OKTAY KƏRİMOV",
        "Z.KAZIMZADƏ",
        "HƏSƏNOĞLU",
        "KAVEROÇKİN",
        "P.ÇAYKOVSKİ",
        "HÜSEYN ARİF",
        "HACI MURAD",
        "BAKI-BATUMİ",
        "NEMƏT QULİYEV",
        "R.AXUNDOV",
        "AKAD.H.ƏLİYEV",
        "RƏHİM ŞIXƏLİYEV",
        "YUSİFZADƏ",
        "E.QOCAYEV",
        "TARZƏN H.MƏMMƏDOV",
        "İ.HİDAYƏTZADƏ",
        "T.ƏLİYEV",
        "MƏMMƏD ARAZ",
        "V.PLOTNİKOV",
        "Ə.ORUCƏLİYEV",
        "Z.BÜNYADOV",
        "İ.DADAŞOV",
        "ƏLƏSGƏR QAYIBOV",
        "M.ARİF",
        "M.QASQAY",
        "Ə.ƏBDÜLRƏHİMOV",
        "İZZƏT HƏMİDOV",
        "AZADLIQ",
        "ARİF HEYDƏROV",
        "N.SÜLEYMANOV",
        "ŞAHİN MUSTAFAYEV",
        "Ə.VAHİD",
        "Ü.BÜNYADZADƏ",
        "NAZİM HACIYEV",
        "24-CÜ KORPÜ",
        "1-Cİ MƏDƏN",
        "Y.HÜSEYNOV",
        "22-Cİ DAĞLIQ",
        "SÜD FERMASI",
        "ÇAPAYEV",
        "E. NƏCƏFOV",
        "FAİQ RÜSTƏMOV",
        "28 MAY",
        "ZABİTLƏR EVİ",
        "S.S. AXUNDOV",
        "GƏNCLƏR DOSTLUĞU",
        "H.SULTANOV",
        "ƏHMƏD QASIMOV",
        "XURŞUD AĞAYEV",
        "NATƏVAN",
        "YENİ MASSİV",
        "MƏLİK ASLANOV KÜÇƏSİ,",
        "VİDADİ",
        "8 MART",
        "İ.HACIYEV",
        "Y. HƏSƏNOV",
    ]
    villages = [
        "Kələki",
        "Binələr",
        "Davudlu",
        "Birinci aral",
        "İkinci aral",
        "Cardam",
        "Qaradeyin",
        "Qarağan şixlar",
        "Qarağan sədi",
        "Qəribli",
        "Qolqəti",
        "Mürsəl",
        "Şordəhnə",
        "Tofiqi",
        "Yenicə",
        "Ərəbocaği",
        "Hapitli",
        "Ovçulu",
        "Şəkili",
        "Yuxari ağcayazi",
        "Aşaği ağcayazi",
        "Yuxari ləki",
        "Düzqişlaq",
        "Kolayir",
        "Koçvəlili",
        "Xətai",
        "Yenigün",
        "Qaçaq kərəm",
        "Poylu",
        "Tatli",
        "Yaradullu",
        "Xilxina",
        "Mollacəfərli",
        "Ağgöl",
        "Aşaği göycəli",
        "Aşaği kəsəmən",
        "Böyük kəsik",
        "Dağ kəsəmən",
        "Eynalli",
        "Göycəli",
        "Həsənsu",
        "Kolxələfli",
        "Koçəsgər",
        "Köhnəqişlaq",
        "Qarahəsənli",
        "Soyuqbulaqlar",
        "Qiraq kəsəmən",
        "Didivar",
        "Muğanli",
        "Pirili",
        "Sadiqli",
        "Uzunoba",
        "Qaraməmmədli",
        "Navahi",
        "Ülgüc",
        "Ərəbmehdibəy",
        "Dədəli",
        "Qasimbəyli",
        "Ərəbsarvan",
        "Haciqədirli",
        "Göydəlləkli",
        "Ərəbuşaği",
        "Ağalarbəyli",
        "Maşadqanli",
        "Aratli curuğlu",
        "Keşdiməz",
        "Bozavand",
        "Ağarx",
        "Qarabağli",
        "Xanbulaq",
        "Kəndaxan",
        "Yenilik",
        "Kövlüc",
        "Elabad",
        "Yenikənd",
        "Hingar",
        "Girdə",
        "Gursulu",
        "Qaraqoyunlu",
        "Musabəyli",
        "İlxiçi",
        "Hacisəmədli",
        "Qəşəd",
        "Kəndoba",
        "Cəfərli",
        "Haciuşaği",
        "Cəlayir",
        "Abasxanli",
        "Kalva",
        "Suraxani",
        "Dilman",
        "Haciman",
        "Xatman",
        "Növcü",
        "Axundlu",
        "Məlikçobanli",
    ]

    def house_number(self):
        """
        :example: 'm. 49'
        """
        return self.numerify(self.random_element(self.house_number_formats))

    def city(self):
        """
        :example: 'Xankəndi'
        """
        return self.random_element(self.cities)

    def city_suffix(self):
        """
        :example: 'ş.'
        """
        return self.random_element(self.city_suffixes)

    def street(self):
        """
        :example: 'A.AĞAYEV'
        """
        return self.random_element(self.streets)

    def street_suffix(self):
        """
        :example: 'küç.'
        """
        return self.random_element(self.street_suffixes)

    def village(self):
        """
        :example: 'Didivar'
        """
        return self.random_element(self.villages)

    def village_suffix(self):
        """
        :example: 'k.'
        """
        return self.random_element(self.village_suffixes)

    def district(self):
        """
        :example: 'Babək'
        """
        return self.random_element(self.districts)

    def district_suffix(self):
        """
        :example: 'r.'
        """
        return self.random_element(self.district_suffixes)

    def settlement(self):
        """
        :example: 'Horadiz'
        """
        return self.random_element(self.settlements)

    def settlement_suffix(self):
        """
        :example: 'qəs.'
        """
        return self.random_element(self.settlement_suffixes)

    def administrative_unit(self):
        """
        :example: 'Xankəndi'
        """
        return self.random_element(self.districts + self.cities)

    def postcode(self):
        """
        :example: 'AZ1027'
        """
        index = self.generator.random.randint(900, 6600)
        return "AZ%04d" % index if index > 999 else "AZ0%03d" % index

    def postalcode(self):
        return self.postcode()
