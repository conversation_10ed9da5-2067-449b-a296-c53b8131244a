import { useState, useEffect, createContext, useContext } from 'react';
import { authAPI } from '../utils/api';
import { getToken, setToken, removeToken, getUser, setUser, removeUser } from '../utils/auth';
import toast from 'react-hot-toast';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUserState] = useState(getUser());
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(() => {
    const token = getToken();
    const storedUser = getUser();
    return !!(token && storedUser);
  });

  useEffect(() => {
    const initAuth = async () => {
      const token = getToken();
      const storedUser = getUser();

      if (token && storedUser) {
        try {
          const response = await authAPI.verifyToken();
          setUserState(response.data.user);
          setUser(response.data.user);
          setIsAuthenticated(true);
        } catch (error) {
          console.error('Token verification failed:', error);
          removeToken();
          removeUser();
          setUserState(null);
          setIsAuthenticated(false);
        }
      } else {
        setIsAuthenticated(false);
        setUserState(null);
      }
      setLoading(false);
    };

    initAuth();
  }, []);

  // Sync authentication state when localStorage changes
  useEffect(() => {
    const handleStorageChange = () => {
      const token = getToken();
      const currentUser = getUser();
      const shouldBeAuthenticated = !!(token && currentUser);

      if (shouldBeAuthenticated !== isAuthenticated) {
        setIsAuthenticated(shouldBeAuthenticated);
        setUserState(currentUser);
      }
    };

    // Listen for storage changes (useful for multiple tabs)
    window.addEventListener('storage', handleStorageChange);

    // Also check periodically to ensure sync
    const interval = setInterval(handleStorageChange, 1000);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(interval);
    };
  }, [isAuthenticated]);

  const login = async (credentials) => {
    try {
      const response = await authAPI.login(credentials);
      const { access_token, user } = response.data;

      // Store token and user in localStorage first
      setToken(access_token);
      setUser(user);

      // Update React state synchronously
      setUserState(user);
      setIsAuthenticated(true);

      toast.success('Login successful!');

      // Return success with a small delay to ensure state propagation
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({ success: true, user });
        }, 50);
      });
    } catch (error) {
      console.error('Login error:', error);
      const message = error.response?.data?.error || 'Login failed';
      toast.error(message);
      return { success: false, error: message };
    }
  };

  const register = async (userData) => {
    try {
      const response = await authAPI.register(userData);
      const { access_token, user } = response.data;
      
      setToken(access_token);
      setUser(user);
      setUserState(user);
      setIsAuthenticated(true);
      
      toast.success('Registration successful!');
      return { success: true, user };
    } catch (error) {
      const message = error.response?.data?.error || 'Registration failed';
      toast.error(message);
      return { success: false, error: message };
    }
  };

  const logout = async () => {
    try {
      await authAPI.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      removeToken();
      removeUser();
      setUserState(null);
      setIsAuthenticated(false);
      toast.success('Logged out successfully');
    }
  };

  const value = {
    user,
    loading,
    isAuthenticated,
    login,
    register,
    logout,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
